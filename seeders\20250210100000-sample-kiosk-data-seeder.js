"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");
const logger = require("../config/logger");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      logger.info("Starting comprehensive kiosk data seeding...");

      // Step 1: Create Kiosk Groups
      const kioskGroups = [
        {
          kiosk_group_id: uuidv4(),
          name: "Main Lobby Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Emergency Department Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("kiosk_group", kioskGroups, { transaction });
      logger.info(`Created ${kioskGroups.length} kiosk groups`);

      // Step 2: Get existing facilities for associations
      const facilities = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility LIMIT 2;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (facilities.length === 0) {
        logger.warn("No facilities found. Creating basic facility for testing.");
        const facilityId = uuidv4();
        await queryInterface.bulkInsert("facility", [{
          facility_id: facilityId,
          name: "Test Hospital",
          address: "123 Test Street",
          city: "Test City",
          state: "TS",
          zip_code: "12345",
          phone: "+**********",
          email: "<EMAIL>",
          created_at: new Date(),
          updated_at: new Date(),
        }], { transaction });
        facilities.push({ facility_id: facilityId });
      }

      // Step 3: Create Buildings, Floors, and Rooms
      let buildings = [];
      let floors = [];
      let rooms = [];

      if (facilities.length > 0) {
        buildings = [
          {
            building_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            name: "Main Hospital Building",
            address: "123 Medical Center Drive",
            year_constructed: 2015,
            building_code: "MHB-001",
            status: 0,
            type: 3,
            occupancy_type: 5,
            phone: "******-0101",
            email: "<EMAIL>",
            geo_location_code: -122.4194,
            other_code: "MHB",
            building_url: "https://hospital.com/buildings/main",
            notes: "Primary hospital building with kiosk services",
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("building", buildings, { transaction });

        floors = [
          {
            floor_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_number: 1,
            status: 0,
            total_square_footage: 15000.50,
            max_occupancy: 200,
            occupancy_type: 5,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("floor", floors, { transaction });

        rooms = [
          {
            room_id: uuidv4(),
            facility_id: facilities[0].facility_id,
            building_id: buildings[0].building_id,
            floor_id: floors[0].floor_id,
            room_number: "101",
            max_occupancy: 20,
            area: 500,
            primary_contact_name: "Kiosk Manager",
            primary_contact_number: "5551234567",
            primary_contact_email: "<EMAIL>",
            status: 0,
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("room", rooms, { transaction });
      }

      // Step 4: Create Devices (exactly 2 as requested)
      const devices = [
        {
          device_id: uuidv4(),
          name: "Main Lobby Check-in Kiosk 1",
          identifier: "KIOSK-LOBBY-001",
          kiosk_group_id: kioskGroups[0].kiosk_group_id,
          facility_id: facilities[0].facility_id,
          building_id: buildings.length > 0 ? buildings[0].building_id : null,
          floor_id: floors.length > 0 ? floors[0].floor_id : null,
          room_id: rooms.length > 0 ? rooms[0].room_id : null,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Emergency Department Kiosk",
          identifier: "KIOSK-ED-001",
          kiosk_group_id: kioskGroups[1].kiosk_group_id,
          facility_id: facilities[0].facility_id,
          building_id: buildings.length > 0 ? buildings[0].building_id : null,
          facility_floor_id: floors.length > 0 ? floors[0].floor_id : null,
          room_id: rooms.length > 0 ? rooms[0].room_id : null,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("device", devices, { transaction });
      logger.info(`Created ${devices.length} devices`);

      // Step 5: Create NDA Templates (combining existing NDA seeder data)
      const ndaTemplates = [
        {
          nda_template_id: uuidv4(),
          name: "Standard Hospital NDA",
          version: 1,
          document_url: "https://www.mayoclinic.org/documents/confidentiality-jax-pdf/doc-20079517",
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Emergency Department NDA",
          version: 1,
          document_url: "https://nondisclosureagreement.com/wp-content/uploads/2020/11/Basic-Non-Disclosure-Agreement.pdf",
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Standard Employee NDA",
          version: 1,
          document_url: "https://example.com/documents/employee-nda-v1.pdf",
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Standard Employee NDA",
          version: 2,
          document_url: "https://example.com/documents/employee-nda-v2.pdf",
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Contractor NDA",
          version: 1,
          document_url: "https://example.com/documents/contractor-nda-v1.pdf",
          jurisdiction: "New York",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Vendor Partnership NDA",
          version: 1,
          document_url: "https://www.mayoclinic.org/documents/confidentiality-jax-pdf/doc-20079517",
          jurisdiction: "Delaware",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Healthcare Data NDA",
          version: 1,
          document_url: "https://example.com/documents/healthcare-nda-v1.pdf",
          jurisdiction: "Federal",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("nda_template", ndaTemplates, { transaction });
      logger.info(`Created ${ndaTemplates.length} NDA templates`);

      // Step 6: Create NDA Agreements
      const identities = await queryInterface.sequelize.query(
        "SELECT identity_id FROM identity LIMIT 10;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const ndaAgreements = [];
      if (identities.length > 0) {
        const agreementData = [
          {
            template_index: 0,
            effective_date: new Date("2024-01-15"),
            expiration_date: new Date("2025-01-15"),
            status: 1, // Signed
            signed_at: new Date("2024-01-15T10:30:00Z"),
          },
          {
            template_index: 1,
            effective_date: new Date("2024-02-01"),
            expiration_date: new Date("2025-02-01"),
            status: 1, // Signed
            signed_at: new Date("2024-02-01T14:20:00Z"),
          },
          {
            template_index: 2,
            effective_date: new Date("2024-03-10"),
            expiration_date: new Date("2024-12-31"),
            status: 0, // Pending
            signed_at: null,
          },
          {
            template_index: 3,
            effective_date: new Date("2024-01-01"),
            expiration_date: null,
            status: 1, // Signed
            signed_at: new Date("2024-01-01T09:00:00Z"),
          },
          {
            template_index: 4,
            effective_date: new Date("2023-12-01"),
            expiration_date: new Date("2024-12-01"),
            status: 2, // Expired
            signed_at: new Date("2023-12-01T16:45:00Z"),
          },
        ];

        // Create agreements using available identities and templates
        for (let i = 0; i < agreementData.length && i < Math.min(identities.length, ndaTemplates.length); i++) {
          const data = agreementData[i];
          const identityIndex = i % identities.length;
          const templateIndex = Math.min(data.template_index, ndaTemplates.length - 1);

          ndaAgreements.push({
            nda_agreement_id: uuidv4(),
            identity_id: identities[identityIndex].identity_id,
            nda_template_id: ndaTemplates[templateIndex].nda_template_id,
            effective_date: data.effective_date,
            expiration_date: data.expiration_date,
            status: data.status,
            signed_at: data.signed_at,
            created_at: new Date(),
            updated_at: new Date(),
          });
        }

        if (ndaAgreements.length > 0) {
          await queryInterface.bulkInsert("nda_agreement", ndaAgreements, { transaction });
          logger.info(`Created ${ndaAgreements.length} NDA agreements`);
        }
      }

      // Step 7: Create NDA Signatures
      const ndaSignatures = [];
      if (ndaAgreements.length > 0 && identities.length > 0) {
        const signatureData = [
          {
            agreement_index: 0,
            signer_role: 0, // User
            use_agreement_identity: true,
            signature_method: 0, // E-Signature
            signature: "/signatures/kiosk_user_001_signature.png",
            time_offset: 0,
          },
          {
            agreement_index: 1,
            signer_role: 0, // User
            use_agreement_identity: true,
            signature_method: 2, // Digital Signature
            signature: "/signatures/kiosk_user_002_digital.pdf",
            time_offset: 0,
          },
          {
            agreement_index: 1,
            signer_role: 1, // Admin
            use_agreement_identity: false,
            signature_method: 0, // E-Signature
            signature: "/signatures/kiosk_admin_001_signature.png",
            time_offset: 3600000, // 1 hour later
          },
          {
            agreement_index: 3,
            signer_role: 3, // External Party
            use_agreement_identity: true,
            signature_method: 1, // Manual Upload
            signature: "/signatures/kiosk_guest_signature.pdf",
            time_offset: 0,
          },
        ];

        // Create signatures for signed agreements only
        for (let i = 0; i < signatureData.length; i++) {
          const data = signatureData[i];
          const agreementIndex = Math.min(data.agreement_index, ndaAgreements.length - 1);
          const agreement = ndaAgreements[agreementIndex];

          // Skip if agreement doesn't have signed_at (pending agreements)
          if (!agreement.signed_at) {
            continue;
          }

          // Determine signer identity
          let signerIdentityId;
          if (data.use_agreement_identity) {
            signerIdentityId = agreement.identity_id;
          } else {
            const identityIndex = (i + 1) % identities.length;
            signerIdentityId = identities[identityIndex].identity_id;
          }

          // Calculate signature time
          const baseTime = new Date(agreement.signed_at);
          const signedAt = new Date(baseTime.getTime() + data.time_offset);

          ndaSignatures.push({
            nda_signature_id: uuidv4(),
            nda_agreement_id: agreement.nda_agreement_id,
            signer_role: data.signer_role,
            signer_id: signerIdentityId,
            signed_at: signedAt,
            signature_method: data.signature_method,
            signature: data.signature,
            created_at: new Date(),
            updated_at: new Date(),
          });
        }

        if (ndaSignatures.length > 0) {
          await queryInterface.bulkInsert("nda_signature", ndaSignatures, { transaction });
          logger.info(`Created ${ndaSignatures.length} NDA signatures`);
        }
      }

      // Step 8: Create Device Settings
      const deviceSettings = devices.map((device, index) => ({
        device_setting_id: uuidv4(),
        device_id: device.device_id,
        nda_template_id: ndaTemplates[index].nda_template_id,
        shownda: true,
        doctors_office_visit: true,
        showexpeditecheckin: true,
        showwalkinguest: true,
        guest_verification: true,
        re_check_in: true,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("device_setting", deviceSettings, { transaction });
      logger.info(`Created ${deviceSettings.length} device settings`);

      // Step 9: Get existing patients and appointments for guest associations
      const patients = await queryInterface.sequelize.query(
        "SELECT patient_id, first_name, last_name, phone FROM patient LIMIT 10;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      const appointments = await queryInterface.sequelize.query(
        "SELECT appointment_id, patient_id, facility_id FROM appointment LIMIT 10;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      // Step 10: Create additional test patients if needed
      const additionalPatients = [];
      if (patients.length < 5) {
        // Create outpatient test patient
        const outpatientId = uuidv4();
        additionalPatients.push({
          patient_id: outpatientId,
          first_name: "Sarah",
          last_name: "Connor",
          birth_date: new Date("1990-05-15"),
          phone: "+**********", // For outpatient testing
          email: "<EMAIL>",
          gender: 1,
          marital_status: 0,
          preferred_language: 0,
          created_at: new Date(),
          updated_at: new Date(),
        });

        // Create inpatient test patient
        const inpatientId = uuidv4();
        additionalPatients.push({
          patient_id: inpatientId,
          first_name: "John",
          last_name: "Smith",
          birth_date: new Date("1985-03-20"),
          phone: "+**********", // Last 4: 1234 for inpatient testing
          email: "<EMAIL>",
          gender: 0,
          marital_status: 1,
          preferred_language: 0,
          created_at: new Date(),
          updated_at: new Date(),
        });

        await queryInterface.bulkInsert("patient", additionalPatients, { transaction });
        logger.info(`Created ${additionalPatients.length} additional test patients`);

        // Create corresponding appointments
        const additionalAppointments = [
          {
            appointment_id: uuidv4(),
            hl7_appointment_id: Math.floor(Math.random() * 90000) + 10000,
            patient_id: outpatientId,
            appointment_date: new Date(),
            type: 0, // Outpatient
            status: 0, // Active
            facility_id: facilities[0].facility_id,
            provider_name: "Dr. Outpatient",
            department: "Outpatient Services",
            created_at: new Date(),
            updated_at: new Date(),
          },
          {
            appointment_id: uuidv4(),
            hl7_appointment_id: Math.floor(Math.random() * 90000) + 10000,
            patient_id: inpatientId,
            appointment_date: new Date(),
            type: 1, // Inpatient
            status: 0, // Active
            facility_id: facilities[0].facility_id,
            provider_name: "Dr. Inpatient",
            department: "Inpatient Services",
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];

        await queryInterface.bulkInsert("appointment", additionalAppointments, { transaction });
        logger.info(`Created ${additionalAppointments.length} additional test appointments`);

        // Update patients and appointments arrays
        patients.push(...additionalPatients);
        appointments.push(...additionalAppointments);
      }

      // Step 11: Create sample patient guests and appointment guests
      if (patients.length > 0 && appointments.length > 0) {
        const testScenarios = [
          {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone: "+**********", // Last 4 digits: 7890
            guest_type: 1, // Friend Family
            relationship_type: 0, // Spouse
            guest_pin: "123456",
            scenario: "Standard guest with PIN for fetch testing"
          },
          {
            first_name: "Jane",
            last_name: "Smith",
            email: "<EMAIL>",
            phone: "+**********", // Last 4 digits: 4321
            guest_type: 0, // Guest
            relationship_type: 1, // Child
            guest_pin: "654321",
            scenario: "Child guest for relationship testing"
          },
          {
            first_name: "Bob",
            last_name: "Wilson",
            email: "<EMAIL>",
            phone: "+**********", // Last 4 digits: 3456
            guest_type: 1, // Friend Family
            relationship_type: 3, // Emergency Contact
            guest_pin: "789012",
            scenario: "Emergency contact for testing"
          },
        ];

        const patientGuests = [];
        const appointmentGuests = [];

        for (let i = 0; i < Math.min(testScenarios.length, patients.length, appointments.length); i++) {
          const scenario = testScenarios[i];
          const patient = patients[i];
          const appointment = appointments[i];

          const patientGuestId = uuidv4();
          const appointmentGuestId = uuidv4();

          // Create patient guest
          patientGuests.push({
            patient_guest_id: patientGuestId,
            patient_id: patient.patient_id,
            first_name: scenario.first_name,
            last_name: scenario.last_name,
            email: scenario.email,
            phone: scenario.phone,
            organization: faker.company.name(),
            guest_type: scenario.guest_type,
            relationship_type: scenario.relationship_type,
            is_emergency_contact: scenario.relationship_type === 3,
            emergency_contact_priority: scenario.relationship_type === 3 ? 1 : null,
            can_make_decisions: scenario.relationship_type === 0 || scenario.relationship_type === 3,
            has_custody: scenario.relationship_type === 1,
            lives_with_patient: scenario.relationship_type === 0 || scenario.relationship_type === 1,
            relationship_notes: `Test ${scenario.scenario}`,
            effective_from: new Date(),
            effective_to: null,
            reason: scenario.guest_type === 2 ? "Test denial reason" : null,
            denied_on: scenario.guest_type === 2 ? new Date() : null,
            image: null,
            is_walkin: false,
            friends_and_family: scenario.guest_type === 1,
            birth_date: faker.date.past({ years: 30, refDate: new Date(2000, 0, 1) }),
            created_at: new Date(),
            updated_at: new Date(),
          });

          // Create appointment guest
          appointmentGuests.push({
            appointment_guest_id: appointmentGuestId,
            appointment_id: appointment.appointment_id,
            patient_guest_id: patientGuestId,
            start_date: new Date(),
            start_time: "09:00:00",
            duration: 60,
            escort_name: null,
            facility_id: appointment.facility_id || facilities[0].facility_id,
            screening: null,
            guest_pin: scenario.guest_pin,
            status: 4, // Registered status for testing
            arrival_time: null,
            departure_time: null,
            created_at: new Date(),
            updated_at: new Date(),
          });
        }

        if (patientGuests.length > 0) {
          await queryInterface.bulkInsert("patient_guest", patientGuests, { transaction });
          logger.info(`Created ${patientGuests.length} patient guests for kiosk testing`);
        }

        if (appointmentGuests.length > 0) {
          await queryInterface.bulkInsert("appointment_guest", appointmentGuests, { transaction });
          logger.info(`Created ${appointmentGuests.length} appointment guests for kiosk testing`);
        }
      }

      await transaction.commit();
      logger.info("Comprehensive kiosk data seeding completed successfully");
    } catch (error) {
      logger.error("Kiosk data seeding error:", error);
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Delete in reverse order to maintain referential integrity
      await queryInterface.bulkDelete("appointment_guest", null, { transaction });
      await queryInterface.bulkDelete("patient_guest", null, { transaction });

      // Delete additional test appointments and patients created by this seeder
      await queryInterface.sequelize.query(
        "DELETE FROM appointment WHERE provider_name IN ('Dr. Outpatient', 'Dr. Inpatient')",
        { transaction }
      );
      await queryInterface.sequelize.query(
        "DELETE FROM patient WHERE email IN ('<EMAIL>', '<EMAIL>')",
        { transaction }
      );

      await queryInterface.bulkDelete("device_setting", null, { transaction });
      await queryInterface.bulkDelete("nda_signature", null, { transaction });
      await queryInterface.bulkDelete("nda_agreement", null, { transaction });
      await queryInterface.bulkDelete("nda_template", null, { transaction });
      await queryInterface.bulkDelete("device", null, { transaction });
      await queryInterface.bulkDelete("room", null, { transaction });
      await queryInterface.bulkDelete("floor", null, { transaction });
      await queryInterface.bulkDelete("building", null, { transaction });
      await queryInterface.bulkDelete("kiosk_group", null, { transaction });

      await transaction.commit();
      logger.info("Kiosk data seeder rollback completed successfully");
    } catch (error) {
      logger.error("Kiosk data seeder rollback error:", error);
      await transaction.rollback();
      throw error;
    }
  },
};
